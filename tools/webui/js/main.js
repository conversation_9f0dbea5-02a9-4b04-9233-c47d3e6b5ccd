/**
 * Hugo 文章编辑器 - 主要功能
 */

// 全局变量
let vditor = null; // Vditor 编辑器实例

// DOM 元素
const elements = {
    qmojiPicker: null,
    qmojiPickerContent: null,
    // 按钮
    toggleQmojiBtn: null,
    closeQmojiBtn: null,
};

/**
 * 初始化编辑器
 */
async function initEditor() {
    // 初始化 Vditor 编辑器
    vditor = new Vditor("vditor", {
        height: 500,
        mode: "sv", // 默认为分屏预览模式
        theme: "classic",
        lang: "zh_CN",
        placeholder: "开始编写你的文章...",
        toolbar: [
            {
                name: "qmoji",
                tip: "插入 QQ 表情",
                icon: '<svg viewBox="0 0 32 32" width="16" height="16"><path d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14 14-6.2 14-14S23.8 2 16 2zm0 26C9.4 28 4 22.6 4 16S9.4 4 16 4s12 5.4 12 12-5.4 12-12 12z"/><path d="M12 14c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm8 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/><path d="M16 20c-2.3 0-4.3 1.5-5.1 3.7-.2.5.2 1.1.7 1.1h8.8c.5 0 .9-.6.7-1.1C20.3 21.5 18.3 20 16 20z"/></svg>',
                click: () => {
                    toggleQmojiPicker(true);
                },
            },
            "headings",
            "bold",
            "italic",
            "strike",
            "link",
            "|",
            "list",
            "ordered-list",
            "check",
            "outdent",
            "indent",
            "|",
            "quote",
            "line",
            "code",
            "inline-code",
            "insert-before",
            "insert-after",
            "|",
            "upload",
            "table",
            "|",
            "undo",
            "redo",
            "|",
            "fullscreen",
            "preview",
            "outline",
            "help",
            "|",
            "edit-mode",
        ],
        emoji: {
            // 自定义表情面板
            parse: true,
            // 使用自定义的QQ表情数据
            custom: async () => {
                const emojis = await loadQmojiData();
                if (!emojis || emojis.length === 0) {
                    console.error("无法加载QQ表情数据");
                    return {};
                }

                // 按类型分组表情
                const groupedEmojis = groupEmojisByType(emojis);

                // 创建表情映射
                const emojiMap = {};

                // 添加普通表情
                groupedEmojis.normal.forEach((emoji) => {
                    const emojiName = emoji.describe.startsWith("/")
                        ? emoji.describe.substring(1)
                        : emoji.describe;
                    emojiMap[emojiName] = {
                        url: getEmojiThumbUrl(emoji),
                        title: emojiName,
                        content: `{{< qq-emoji "${emojiName}" >}}`,
                    };
                });

                // 添加超级表情
                groupedEmojis.super.forEach((emoji) => {
                    const emojiName = emoji.describe.startsWith("/")
                        ? emoji.describe.substring(1)
                        : emoji.describe;
                    emojiMap[emojiName] = {
                        url: getEmojiThumbUrl(emoji),
                        title: emojiName,
                        content: `{{< qq-emoji "${emojiName}" >}}`,
                    };
                });

                return emojiMap;
            },
        },
        cache: {
            enable: true,
            id: "hugo-editor",
        },
        preview: {
            hljs: {
                enable: true,
                style: "github",
            },
            math: {
                inlineDigit: true,
                engine: "KaTeX",
            },
            markdown: {
                // 配置链接处理，防止出现 undefined 前缀
                linkBase: "", // 设置为空字符串，不添加基础路径
                linkPrefix: "", // 设置为空字符串，不添加前缀
                autoSpace: false,
                gfmAutoLink: true,
                fixTermTypo: false,
                toc: false,
                footnotes: true,
                codeBlockPreview: true,
                mathBlockPreview: true,
                paragraphBeginningSpace: false,
                sanitize: true,
                listStyle: false,
                mark: false,
            },
            /**
             * 自定义预览渲染 - 修复链接和转换 Qmoji
             * @param {string} html - 渲染后的 HTML 内容
             * @returns {string} 处理后的 HTML
             */
            transform: (html) => {
                let processedHtml = html;

                // 修复链接和图片中的 undefined 前缀问题
                processedHtml = processedHtml.replace(
                    /href=["']undefined+([^"']*?)["']/g,
                    'href="$1"'
                );
                processedHtml = processedHtml.replace(
                    /src=["']undefined+([^"']*?)["']/g,
                    'src="$1"'
                );
                processedHtml = processedHtml.replace(/undefined+/g, "");
                processedHtml = processedHtml.replace(/([^:])\/{2,}/g, "$1/");

                return processedHtml;
            },
        },
        after: () => {
            // 编辑器初始化完成后的回调
        },
    });

    // 初始化 QQ 表情数据
    await initQmojiPicker();

    // 缓存表情数据供预览使用
    window.cachedQmojiData = await loadQmojiData();
}

/**
 * 初始化 QQ 表情选择器
 */
async function initQmojiPicker() {
    // 加载表情数据
    const emojis = await loadQmojiData();
    if (!emojis || emojis.length === 0) {
        console.error("无法加载QQ表情数据");
        return;
    }

    // 按类型分组表情
    const groupedEmojis = groupEmojisByType(emojis);

    // 清空现有内容
    elements.qmojiPickerContent.innerHTML = "";

    // 创建分类标签
    const tabContainer = document.createElement("div");
    tabContainer.className = "qmoji-tabs";
    tabContainer.innerHTML = `
        <button class="qmoji-tab active" data-tab="normal">普通表情</button>
        <button class="qmoji-tab" data-tab="super">超级表情</button>
    `;
    elements.qmojiPickerContent.appendChild(tabContainer);

    // 创建表情容器
    const normalContainer = document.createElement("div");
    normalContainer.className = "qmoji-container active";
    normalContainer.dataset.tabContent = "normal";

    const superContainer = document.createElement("div");
    superContainer.className = "qmoji-container";
    superContainer.dataset.tabContent = "super";
    superContainer.style.display = "none";

    // 添加普通表情
    groupedEmojis.normal.forEach((emoji) => {
        const emojiItem = createEmojiItem(emoji);
        normalContainer.appendChild(emojiItem);
    });

    // 添加超级表情
    groupedEmojis.super.forEach((emoji) => {
        const emojiItem = createEmojiItem(emoji);
        superContainer.appendChild(emojiItem);
    });

    // 添加到DOM
    elements.qmojiPickerContent.appendChild(normalContainer);
    elements.qmojiPickerContent.appendChild(superContainer);

    // 添加标签切换事件
    const tabs = tabContainer.querySelectorAll(".qmoji-tab");
    tabs.forEach((tab) => {
        tab.addEventListener("click", () => {
            // 更新标签状态
            tabs.forEach((t) => t.classList.remove("active"));
            tab.classList.add("active");

            // 更新内容显示
            const tabName = tab.dataset.tab;
            document
                .querySelectorAll(".qmoji-container")
                .forEach((container) => {
                    if (container.dataset.tabContent === tabName) {
                        container.style.display = "grid";
                    } else {
                        container.style.display = "none";
                    }
                });
        });
    });
}

/**
 * 创建表情项
 * @param {Object} emoji 表情对象
 * @returns {HTMLElement} 表情项元素
 */
function createEmojiItem(emoji) {
    const emojiItem = document.createElement("div");
    emojiItem.className = "qmoji-item tooltip tooltip-top";
    emojiItem.setAttribute(
        "data-tip",
        emoji.describe.startsWith("/")
            ? emoji.describe.substring(1)
            : emoji.describe
    );

    // 创建表情图片
    const img = document.createElement("img");
    img.src = getEmojiThumbUrl(emoji);
    img.alt = emoji.describe;
    img.title = emoji.describe;
    img.className = "w-8 h-8 object-contain";
    emojiItem.appendChild(img);

    // 添加悬停事件以显示提示
    let tooltipTimeout;
    emojiItem.addEventListener("mouseenter", () => {
        tooltipTimeout = setTimeout(() => {
            // 使用daisyUI的tooltip类
            emojiItem.classList.add("tooltip-open");
        }, 500); // 悬停500ms后显示提示
    });

    emojiItem.addEventListener("mouseleave", () => {
        clearTimeout(tooltipTimeout);
        emojiItem.classList.remove("tooltip-open");
    });

    // 确保提示位置正确
    emojiItem.addEventListener("mousemove", () => {
        // 可以根据需要调整提示位置
    });

    // 添加点击事件
    emojiItem.addEventListener("click", () => {
        insertEmojiToEditor(emoji);
    });

    return emojiItem;
}

/**
 * 将表情插入到编辑器
 * @param {Object} emoji 表情对象
 */
function insertEmojiToEditor(emoji) {
    const shortcode = getEmojiShortcode(emoji);
    vditor.insertValue(shortcode);
    toggleQmojiPicker(false);
}

/**
 * 切换 QQ 表情选择器显示状态
 * @param {boolean} show 是否显示
 */
function toggleQmojiPicker(show) {
    if (show === undefined) {
        show = elements.qmojiPicker.classList.contains("hidden");
    }

    if (show) {
        elements.qmojiPicker.classList.remove("hidden");
        // 添加 modal-open 类以显示模态框
        elements.qmojiPicker.classList.add("modal-open");
    } else {
        elements.qmojiPicker.classList.add("hidden");
        // 移除 modal-open 类以隐藏模态框
        elements.qmojiPicker.classList.remove("modal-open");
    }
}

/**
 * 初始化事件监听
 */
function initEventListeners() {
    // 关闭 QQ 表情选择器按钮
    if (elements.closeQmojiBtn) {
        elements.closeQmojiBtn.addEventListener("click", () =>
            toggleQmojiPicker(false)
        );
    }
}

/**
 * 初始化应用
 */
function init() {
    // 获取 DOM 元素
    elements.qmojiPicker = document.getElementById("qmojiPicker");
    elements.qmojiPickerContent = document.querySelector(
        ".qmoji-picker-content"
    );

    // 按钮
    elements.closeQmojiBtn = document.getElementById("closeQmojiBtn");

    // 初始化编辑器
    initEditor();

    // 初始化事件监听
    initEventListeners();
}

// 当 DOM 加载完成后初始化应用
document.addEventListener("DOMContentLoaded", init);

// 添加 CSS 样式
const style = document.createElement("style");
style.textContent = `
.qmoji-tabs {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
}

.qmoji-tab {
    padding: 8px 15px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    margin-right: 10px;
    color: #555;
}

.qmoji-tab.active {
    border-bottom-color: #3498db;
    color: #3498db;
}

.qmoji-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 10px;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px 0;
}
`;
document.head.appendChild(style);
