<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title><PERSON> 文章编辑器 - 支持 QQ 表情</title>
        <link
            href="https://cdn.jsdelivr.net/npm/daisyui@5"
            rel="stylesheet"
            type="text/css"
        />
        <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
        <link
            rel="stylesheet"
            href="https://cdn.jsdelivr.net/npm/vditor@3.9.6/dist/index.css"
        />
        <script src="https://cdn.jsdelivr.net/npm/vditor@3.9.6/dist/index.min.js"></script>
    </head>
    <body>
        <div class="container mx-auto max-w-6xl p-4">

            <main>
                <div class="editor-container">
                    <div id="vditor" class="markdown-editor"></div>

                    <div
                        id="qmojiPicker"
                        class="qmoji-picker hidden modal modal-bottom sm:modal-middle"
                    >
                        <div class="modal-box max-w-4xl">
                            <div
                                class="qmoji-picker-header modal-header flex justify-between items-center mb-4"
                            >
                                <h3 class="text-xl font-bold">QQ 表情选择器</h3>
                                <button
                                    id="closeQmojiBtn"
                                    class="btn btn-sm btn-circle btn-ghost"
                                    onclick="toggleQmojiPicker()"
                                >
                                    ✕
                                </button>
                            </div>
                            <div class="qmoji-picker-content"></div>
                        </div>
                    </div>
                </div>
            </main>

        </div>

        <script src="js/qmoji-data.js"></script>
        <script src="js/main.js"></script>
    </body>
</html>
