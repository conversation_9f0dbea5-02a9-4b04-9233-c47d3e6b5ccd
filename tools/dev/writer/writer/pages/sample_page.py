from __future__ import annotations

from dataclasses import KW_ONLY, field
import typing as t

import rio

from .. import components as comps

@rio.page(
    name="首页",
    url_segment="",
)
class HomePage(rio.Component):
    """
    Hugo 博客文章编辑器的首页，提供导航到 Front Matters 设置页面。
    """

    def build(self) -> rio.Component:
        return rio.Column(
            rio.Text("Hugo 博客文章编辑器", style="heading1"),
            rio.Text(
                "这是一个用于创建和编辑 Hugo 博客文章的工具。",
                style="text",
            ),
            rio.Button(
                "创建新文章",
                on_press=self._on_create_article,
                style="major",
            ),
            spacing=2,
            margin=2,
            align_x=0.5,
            align_y=0,
        )
    
    def _on_create_article(self) -> None:
        """
        处理创建新文章按钮点击事件，导航到 Front Matters 设置页面。
        """
        self.session.navigate_to("/front-matter")
