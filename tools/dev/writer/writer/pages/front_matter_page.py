from __future__ import annotations

from dataclasses import KW_ONLY, field
import typing as t
import datetime

import rio

from .. import components as comps

# 全局变量用于存储 Front Matters 数据
_global_front_matter_data: dict[str, t.Any] = {}

@rio.page(
    name="Front Matters 设置",
    url_segment="front-matter",
)
class FrontMatterPage(rio.Component):
    """
    Front Matters 设置页面，用于创建新文章并设置 Front Matters。
    """
    
    def build(self) -> rio.Component:
        # 在 build 方法中创建 FrontMatterForm 组件
        front_matter_form = comps.FrontMatterForm(
            on_submit=self.on_front_matter_submit,
        )
        
        return rio.Column(
            rio.Text("Hugo 博客文章编辑器", style="heading1"),
            rio.Text("设置 Front Matters", style="heading2"),
            
            # Front Matters 表单
            front_matter_form,
            
            spacing=1,
            margin=2,
        )
    
    def on_front_matter_submit(self, form_data: comps.FrontMatterForm) -> None:
        """
        处理 Front Matters 表单提交事件，导航到内容编辑页面。
        """
        global _global_front_matter_data
        
        # 收集 Front Matters 数据
        _global_front_matter_data = {
            "title": form_data.title,
            "date": form_data.date.isoformat(),
            "draft": form_data.draft,
            "author": form_data.author,
            "keywords": [k.strip() for k in form_data.keywords.split(",") if k.strip()],
            "categories": [c.strip() for c in form_data.categories.split(",") if c.strip()],
            "tags": [t.strip() for t in form_data.tags.split(",") if t.strip()],
            "noclick": form_data.noclick,
            "status": form_data.status,
            "statusCate": form_data.statusCate,
            "categoryLink": form_data.categoryLink,
            "buy": form_data.buy,
            "buyLink": form_data.buyLink,
            "buyName": form_data.buyName,
            "buyInfo": form_data.buyInfo,
            "buyImage": form_data.buyImage,
            "buyButtonText": form_data.buyButtonText,
            "weight": form_data.weight,
        }
        
        # 导航到内容编辑页面
        self.session.navigate_to("/content-editor")
