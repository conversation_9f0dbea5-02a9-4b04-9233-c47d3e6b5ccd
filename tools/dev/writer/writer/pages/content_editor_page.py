from __future__ import annotations

from dataclasses import KW_ONLY, field
import typing as t
import datetime
import tomlkit

import rio

from .. import components as comps
from ..pages.front_matter_page import _global_front_matter_data

@rio.page(
    name="内容编辑器",
    url_segment="content-editor",
)
class ContentEditorPage(rio.Component):
    """
    内容编辑器页面，用于编写文章内容。
    """
    
    # Front Matters 数据
    front_matter: dict[str, t.Any] = field(default_factory=dict)
    
    def build(self) -> rio.Component:
        # 从全局变量中获取 Front Matters 数据，但不修改组件状态
        global _global_front_matter_data
        current_front_matter = _global_front_matter_data

        # 在 build 方法中创建 ContentEditor 组件
        content_editor = comps.ContentEditor(
            front_matter=current_front_matter,
            on_save=self.on_save,
            on_back=self.on_back,
        )
        
        return rio.Column(
            rio.Text("Hugo 博客文章编辑器", style="heading1"),
            rio.Text("编辑文章内容", style="heading2"),
            
            # 内容编辑器
            content_editor,
            
            spacing=1,
            margin=2,
        )
    
    def on_save(self) -> None:
        """
        处理保存文章事件，将 Front Matters 和内容保存为 Hugo 文章格式。
        """
        # 从全局变量获取 Front Matters 数据
        global _global_front_matter_data
        front_matter = _global_front_matter_data

        # 在实际应用中，这里需要从 WebView 获取编辑后的内容
        # 由于 Rio 的 WebView 组件可能不支持直接获取内容，
        # 我们暂时使用一个占位符内容
        content = "这里是文章内容，需要从 WebView 获取实际编辑的内容。"

        # 生成文件名（基于标题和日期）
        title = front_matter.get("title", "untitled")
        date_str = datetime.datetime.fromisoformat(front_matter.get("date", datetime.datetime.now().isoformat()))
        date_str = date_str.strftime("%Y-%m-%d")

        # 简化标题为文件名
        filename = title.lower().replace(" ", "-").replace("/", "-").replace("?", "").replace("!", "")
        filename = f"{date_str}-{filename}.md"

        # 构建 TOML 格式的 Front Matters
        toml_doc = tomlkit.document()

        # 添加 Front Matters 字段
        for key, value in front_matter.items():
            if key in ["keywords", "categories", "tags"]:
                # 列表类型
                if isinstance(value, list) and value:
                    toml_doc.add(key, value)
            elif key == "date":
                # 日期类型
                toml_doc.add(key, datetime.datetime.fromisoformat(value))
            elif key in ["draft", "noclick", "status", "buy", "featured"]:
                # 布尔类型
                toml_doc.add(key, bool(value))
            elif key == "weight":
                # 数字类型
                toml_doc.add(key, int(value))
            else:
                # 字符串类型
                if value:  # 只添加非空值
                    toml_doc.add(key, str(value))
        
        # 生成完整的文章内容
        article_content = f"+++\
{tomlkit.dumps(toml_doc)}+++\
\
{content}"
        
        # 在实际应用中，这里应该将文章内容保存到文件
        # 例如：with open(f"content/post/{filename}", "w", encoding="utf-8") as f:
        #           f.write(article_content)
        
        # 显示保存成功的消息
        rio.showToast(
            session=self.session,
            text=f"文章已保存为 {filename}",
            duration=3,
        )
        
        # 导航回 Front Matters 设置页面
        self.session.navigate_to("/front-matter")
    
    def on_back(self) -> None:
        """
        处理返回事件，导航回 Front Matters 设置页面。
        """
        self.session.navigate_to("/front-matter")
