from __future__ import annotations

import asyncio
import json
import threading
import typing as t
from dataclasses import dataclass
import websockets
import websockets.server
from websockets.exceptions import ConnectionClosed
import logging

logger = logging.getLogger(__name__)

@dataclass
class EditorMessage:
    """编辑器消息数据结构"""
    type: str  # 'content_change', 'save_request', 'init', etc.
    data: t.Any
    timestamp: float = 0.0

class WebSocketEditorServer:
    """
    WebSocket 服务器，用于 Rio 应用与 Vditor 编辑器之间的通信
    """
    
    def __init__(self, host: str = "localhost", port: int = 8765):
        self.host = host
        self.port = port
        self.server = None
        self.clients: set[websockets.WebSocketServerProtocol] = set()
        self.current_content = ""
        self.front_matter = {}
        
        # 回调函数
        self.on_content_change: t.Callable[[str], None] = lambda x: None
        self.on_save_request: t.Callable[[str], None] = lambda x: None
        
        self._server_task = None
        self._loop = None
        
    async def register_client(self, websocket: websockets.WebSocketServerProtocol):
        """注册新的客户端连接"""
        self.clients.add(websocket)
        logger.info(f"客户端已连接: {websocket.remote_address}")
        
        # 发送初始数据给新连接的客户端
        await self.send_to_client(websocket, EditorMessage(
            type="init",
            data={
                "content": self.current_content,
                "frontMatter": self.front_matter
            }
        ))
        
    async def unregister_client(self, websocket: websockets.WebSocketServerProtocol):
        """注销客户端连接"""
        self.clients.discard(websocket)
        logger.info(f"客户端已断开: {websocket.remote_address}")
        
    async def send_to_client(self, websocket: websockets.WebSocketServerProtocol, message: EditorMessage):
        """向指定客户端发送消息"""
        try:
            await websocket.send(json.dumps({
                "type": message.type,
                "data": message.data,
                "timestamp": message.timestamp
            }))
        except ConnectionClosed:
            await self.unregister_client(websocket)
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            
    async def broadcast_message(self, message: EditorMessage):
        """向所有客户端广播消息"""
        if self.clients:
            await asyncio.gather(
                *[self.send_to_client(client, message) for client in self.clients],
                return_exceptions=True
            )
            
    async def handle_client_message(self, websocket: websockets.WebSocketServerProtocol, message: str):
        """处理来自客户端的消息"""
        try:
            data = json.loads(message)
            msg_type = data.get("type")
            msg_data = data.get("data")
            
            if msg_type == "content_change":
                # 内容变化
                self.current_content = msg_data
                self.on_content_change(msg_data)
                
            elif msg_type == "save_request":
                # 保存请求
                self.on_save_request(msg_data)
                
            elif msg_type == "ping":
                # 心跳检测
                await self.send_to_client(websocket, EditorMessage(type="pong", data=""))
                
            else:
                logger.warning(f"未知消息类型: {msg_type}")
                
        except json.JSONDecodeError:
            logger.error(f"无效的 JSON 消息: {message}")
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
            
    async def client_handler(self, websocket: websockets.WebSocketServerProtocol, path: str):
        """客户端连接处理器"""
        await self.register_client(websocket)
        try:
            async for message in websocket:
                await self.handle_client_message(websocket, message)
        except ConnectionClosed:
            pass
        finally:
            await self.unregister_client(websocket)
            
    async def start_server(self):
        """启动 WebSocket 服务器"""
        try:
            self.server = await websockets.serve(
                self.client_handler,
                self.host,
                self.port
            )
            logger.info(f"WebSocket 服务器已启动: ws://{self.host}:{self.port}")
        except Exception as e:
            logger.error(f"启动服务器失败: {e}")
            raise
            
    async def stop_server(self):
        """停止 WebSocket 服务器"""
        if self.server:
            self.server.close()
            await self.server.wait_closed()
            logger.info("WebSocket 服务器已停止")
            
    def start_in_thread(self):
        """在新线程中启动服务器"""
        def run_server():
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)
            self._loop.run_until_complete(self.start_server())
            self._loop.run_forever()
            
        self._server_task = threading.Thread(target=run_server, daemon=True)
        self._server_task.start()
        
    def stop(self):
        """停止服务器"""
        if self._loop:
            self._loop.call_soon_threadsafe(self._loop.stop)
            
    def update_content(self, content: str):
        """更新内容并广播给所有客户端"""
        self.current_content = content
        if self._loop and self.clients:
            asyncio.run_coroutine_threadsafe(
                self.broadcast_message(EditorMessage(
                    type="content_update",
                    data=content
                )),
                self._loop
            )
            
    def update_front_matter(self, front_matter: dict):
        """更新 Front Matter 并广播给所有客户端"""
        self.front_matter = front_matter
        if self._loop and self.clients:
            asyncio.run_coroutine_threadsafe(
                self.broadcast_message(EditorMessage(
                    type="front_matter_update",
                    data=front_matter
                )),
                self._loop
            )
