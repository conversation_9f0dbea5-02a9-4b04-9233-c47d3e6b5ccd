from __future__ import annotations

from dataclasses import KW_ONLY, field
import typing as t
import json

import rio

class ContentEditor(rio.Component):
    """
    内容编辑器组件，使用 WebView 引用 assets/index.html 来编辑文章内容。
    """
    
    # Front Matters 数据
    front_matter: dict[str, t.Any] = field(default_factory=dict)
    
    # 文章内容
    content: str = ""
    
    # 回调函数
    on_save: t.Callable[[], None] = lambda: None
    on_back: t.Callable[[], None] = lambda: None
    
    def build(self) -> rio.Component:
        return rio.Column(
            rio.Row(
                rio.Button(
                    "返回 Front Matters 设置",
                    on_press=self._on_back,
                    style="minor",
                ),
                rio.Button(
                    "保存文章",
                    on_press=self._on_save,
                    style="major",
                ),
                spacing=1,
                margin_bottom=1,
            ),
            
            rio.Webview(
                # 使用 assets/index.html 作为编辑器
                # 注意：这里需要将 Front Matters 数据和内容传递给 WebView
                # 由于 Rio 的 WebView 组件可能不支持直接传递数据，
                # 我们可能需要在实际应用中使用其他方法，如通过 URL 参数或本地存储
                content="assets/index.html",
                # 设置 WebView 占用剩余空间
                grow_y=True,
                min_height=600,
            ),
            
            spacing=1,
            margin=2,
        )
    
    def _on_back(self) -> None:
        self.on_back()
    
    def _on_save(self) -> None:
        # 在实际应用中，这里需要从 WebView 获取编辑后的内容
        # 由于 Rio 的 WebView 组件可能不支持直接获取内容，
        # 我们可能需要在实际应用中使用其他方法，如通过 JavaScript 事件或本地存储
        self.on_save()
