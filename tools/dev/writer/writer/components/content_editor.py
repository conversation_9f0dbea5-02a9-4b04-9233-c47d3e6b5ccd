from __future__ import annotations

from dataclasses import KW_ONLY, field
import typing as t
import json

import rio

class ContentEditor(rio.Component):
    """
    内容编辑器组件，使用 WebView 引用 assets/index.html 来编辑文章内容。
    """
    
    # Front Matters 数据
    front_matter: dict[str, t.Any] = field(default_factory=dict)
    
    # 文章内容
    content: str = ""
    
    # 回调函数
    on_save: t.Callable[[], None] = lambda: None
    on_back: t.Callable[[], None] = lambda: None
    
    def build(self) -> rio.Component:
        return rio.Column(
            rio.Row(
                rio.Button(
                    "返回 Front Matters 设置",
                    on_press=self._on_back,
                    style="minor",
                ),
                rio.Button(
                    "保存文章",
                    on_press=self._on_save,
                    style="major",
                ),
                spacing=1,
                margin_bottom=1,
            ),
            
            rio.Webview(
                # 内嵌 HTML 内容，包含 Vditor 编辑器
                content=self._get_editor_html(),
                # # 设置 WebView 占用剩余空间
                grow_y=True,
                min_height=40,
            ),
            
            spacing=1,
            margin=2,
        )
    
    def _on_back(self) -> None:
        self.on_back()
    
    def _on_save(self) -> None:
        # 在实际应用中，这里需要从 WebView 获取编辑后的内容
        # 由于 Rio 的 WebView 组件可能不支持直接获取内容，
        # 我们可能需要在实际应用中使用其他方法，如通过 JavaScript 事件或本地存储
        self.on_save()

    def _get_editor_html(self) -> str:
        """
        生成内嵌的 HTML 内容，包含 Vditor 编辑器和必要的脚本。
        """
        # 将 Front Matters 数据转换为 JSON 字符串，用于传递给编辑器
        front_matter_json = json.dumps(self.front_matter, ensure_ascii=False, default=str)
        content_json = json.dumps(self.content, ensure_ascii=False)

        return f"""
<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Hugo 文章编辑器</title>
        <link
            href="https://cdn.jsdelivr.net/npm/daisyui@5"
            rel="stylesheet"
            type="text/css"
        />
        <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
        <link
            rel="stylesheet"
            href="https://cdn.jsdelivr.net/npm/vditor@3.9.6/dist/index.css"
        />
        <script src="https://cdn.jsdelivr.net/npm/vditor@3.9.6/dist/index.min.js"></script>
    </head>
    <body>
        <div class="container mx-auto max-w-6xl p-4">
            <main>
                <div class="editor-container">
                    <div id="vditor" class="markdown-editor"></div>
                </div>
            </main>
        </div>

        <script>
            // 从 Rio 应用传递的数据
            const frontMatter = {front_matter_json};
            const initialContent = {content_json};

            let vditor;

            // 初始化编辑器
            function initEditor() {{
                vditor = new Vditor("vditor", {{
                    height: 500,
                    mode: "sv", // 分屏预览模式
                    theme: "classic",
                    lang: "zh_CN",
                    placeholder: "开始编写你的文章...",
                    value: initialContent,
                    after: () => {{
                        console.log('Vditor 编辑器初始化完成');
                    }},
                    input: (value) => {{
                        // 当内容改变时，可以通过某种方式通知 Rio 应用
                        console.log('内容已更改:', value.length, '字符');
                    }}
                }});
            }}

            // 获取编辑器内容的函数
            function getEditorContent() {{
                return vditor ? vditor.getValue() : '';
            }}

            // 设置编辑器内容的函数
            function setEditorContent(content) {{
                if (vditor) {{
                    vditor.setValue(content);
                }}
            }}

            // 当 DOM 加载完成后初始化编辑器
            document.addEventListener("DOMContentLoaded", initEditor);
        </script>
    </body>
</html>
        """
