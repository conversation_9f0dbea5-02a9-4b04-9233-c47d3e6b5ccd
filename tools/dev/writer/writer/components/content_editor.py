from __future__ import annotations

from dataclasses import KW_ONLY, field
import typing as t
import json
import threading
import time

import rio
from .websocket_server import WebSocketEditorServer

class ContentEditor(rio.Component):
    """
    内容编辑器组件，使用 WebSocket 与 Vditor 编辑器进行通信。
    """

    # Front Matters 数据
    front_matter: dict[str, t.Any] = field(default_factory=dict)

    # 文章内容
    content: str = ""

    # 回调函数
    on_save: t.Callable[[], None] = lambda: None
    on_back: t.Callable[[], None] = lambda: None

    # WebSocket 服务器实例
    _websocket_server: t.Optional[WebSocketEditorServer] = field(default=None, init=False)
    _server_port: int = field(default=8765, init=False)
    
    def __post_init__(self):
        """组件初始化后的设置"""
        self._setup_websocket_server()

    def _setup_websocket_server(self):
        """设置 WebSocket 服务器"""
        if self._websocket_server is None:
            self._websocket_server = WebSocketEditorServer(port=self._server_port)

            # 设置回调函数
            self._websocket_server.on_content_change = self._on_editor_content_change
            self._websocket_server.on_save_request = self._on_editor_save_request

            # 设置初始数据
            self._websocket_server.current_content = self.content
            self._websocket_server.front_matter = self.front_matter

            # 启动服务器
            self._websocket_server.start_in_thread()

            # 等待服务器启动
            time.sleep(0.5)

    def build(self) -> rio.Component:
        return rio.Column(
            rio.Row(
                rio.Button(
                    "返回 Front Matters 设置",
                    on_press=self._on_back,
                    style="minor",
                ),
                rio.Button(
                    "保存文章",
                    on_press=self._on_save,
                    style="major",
                ),
                spacing=1,
                margin_bottom=1,
            ),
            
            rio.Webview(
                # 使用完整的 Vditor 编辑器，通过 WebSocket 通信
                content=self._get_vditor_html(),
                grow_y=True,
                min_height=600,
            ),
            
            spacing=1,
            margin=2,
        )
    
    def _on_back(self) -> None:
        self._cleanup_websocket_server()
        self.on_back()

    def _on_save(self) -> None:
        # 通过 WebSocket 服务器获取最新内容
        if self._websocket_server:
            self.content = self._websocket_server.current_content
        self.on_save()

    def _on_editor_content_change(self, content: str) -> None:
        """处理来自编辑器的内容变化事件"""
        self.content = content

    def _on_editor_save_request(self, content: str) -> None:
        """处理来自编辑器的保存请求"""
        self.content = content
        self.on_save()

    def _cleanup_websocket_server(self):
        """清理 WebSocket 服务器"""
        if self._websocket_server:
            self._websocket_server.stop()
            self._websocket_server = None

    def _get_vditor_html(self) -> str:
        """
        生成完整的 Vditor 编辑器 HTML，集成 WebSocket 通信和 QQ 表情支持。
        """
        # 将数据转换为 JSON 字符串
        front_matter_json = json.dumps(self.front_matter, ensure_ascii=False, default=str)
        content_json = json.dumps(self.content, ensure_ascii=False)
        websocket_port = self._server_port

        return f"""
<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Hugo 文章编辑器 - 支持 QQ 表情</title>
        <link href="https://cdn.jsdelivr.net/npm/daisyui@5" rel="stylesheet" type="text/css" />
        <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vditor@3.9.6/dist/index.css" />
        <script src="https://cdn.jsdelivr.net/npm/vditor@3.9.6/dist/index.min.js"></script>
    </head>
    <body>
        <div class="container mx-auto max-w-6xl p-4">
            <main>
                <div class="editor-container">
                    <div id="vditor" class="markdown-editor"></div>

                    <!-- QQ 表情选择器 -->
                    <div id="qmojiPicker" class="qmoji-picker hidden modal modal-bottom sm:modal-middle">
                        <div class="modal-box max-w-4xl">
                            <div class="qmoji-picker-header modal-header flex justify-between items-center mb-4">
                                <h3 class="text-xl font-bold">QQ 表情选择器</h3>
                                <button id="closeQmojiBtn" class="btn btn-sm btn-circle btn-ghost" onclick="toggleQmojiPicker()">✕</button>
                            </div>
                            <div class="qmoji-picker-content"></div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <script>
            // WebSocket 连接
            let ws = null;
            let vditor = null;
            let reconnectAttempts = 0;
            const maxReconnectAttempts = 5;

            // 初始数据
            const frontMatter = {front_matter_json};
            const initialContent = {content_json};

            // WebSocket 连接函数
            function connectWebSocket() {{
                try {{
                    ws = new WebSocket('ws://localhost:{websocket_port}');

                    ws.onopen = function(event) {{
                        console.log('WebSocket 连接已建立');
                        reconnectAttempts = 0;
                    }};

                    ws.onmessage = function(event) {{
                        const message = JSON.parse(event.data);
                        handleWebSocketMessage(message);
                    }};

                    ws.onclose = function(event) {{
                        console.log('WebSocket 连接已关闭');
                        if (reconnectAttempts < maxReconnectAttempts) {{
                            setTimeout(() => {{
                                reconnectAttempts++;
                                console.log(`尝试重连 (${{reconnectAttempts}}/${{maxReconnectAttempts}})`);
                                connectWebSocket();
                            }}, 2000);
                        }}
                    }};

                    ws.onerror = function(error) {{
                        console.error('WebSocket 错误:', error);
                    }};
                }} catch (error) {{
                    console.error('WebSocket 连接失败:', error);
                }}
            }}

            // 处理 WebSocket 消息
            function handleWebSocketMessage(message) {{
                switch (message.type) {{
                    case 'init':
                        if (vditor && message.data.content) {{
                            vditor.setValue(message.data.content);
                        }}
                        break;
                    case 'content_update':
                        if (vditor) {{
                            vditor.setValue(message.data);
                        }}
                        break;
                    case 'pong':
                        // 心跳响应
                        break;
                }}
            }}

            // 发送 WebSocket 消息
            function sendWebSocketMessage(type, data) {{
                if (ws && ws.readyState === WebSocket.OPEN) {{
                    ws.send(JSON.stringify({{
                        type: type,
                        data: data,
                        timestamp: Date.now()
                    }}));
                }}
            }}

            // 初始化 Vditor 编辑器
            function initEditor() {{
                vditor = new Vditor("vditor", {{
                    height: 500,
                    mode: "sv",
                    theme: "classic",
                    lang: "zh_CN",
                    placeholder: "开始编写你的文章...",
                    value: initialContent,
                    toolbar: [
                        {{
                            name: "qmoji",
                            tip: "插入 QQ 表情",
                            icon: '<svg viewBox="0 0 32 32" width="16" height="16"><path d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14 14-6.2 14-14S23.8 2 16 2zm0 26C9.4 28 4 22.6 4 16S9.4 4 16 4s12 5.4 12 12-5.4 12-12 12z"/><path d="M12 14c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm8 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/><path d="M16 20c-2.3 0-4.3 1.5-5.1 3.7-.2.5.2 1.1.7 1.1h8.8c.5 0 .9-.6.7-1.1C20.3 21.5 18.3 20 16 20z"/></svg>',
                            click: () => {{
                                toggleQmojiPicker(true);
                            }},
                        }},
                        "headings", "bold", "italic", "strike", "link", "|",
                        "list", "ordered-list", "check", "outdent", "indent", "|",
                        "quote", "line", "code", "inline-code", "insert-before", "insert-after", "|",
                        "table", "undo", "redo", "|", "fullscreen", "edit-mode", "both", "preview", "outline", "content-theme", "code-theme"
                    ],
                    after: () => {{
                        console.log('Vditor 编辑器初始化完成');
                        // 连接 WebSocket
                        connectWebSocket();
                    }},
                    input: (value) => {{
                        // 内容变化时通过 WebSocket 发送给 Rio 应用
                        sendWebSocketMessage('content_change', value);
                    }}
                }});
            }}

            // QQ 表情相关功能
            function toggleQmojiPicker(show) {{
                const picker = document.getElementById('qmojiPicker');
                if (show) {{
                    picker.classList.remove('hidden');
                    if (!picker.dataset.initialized) {{
                        initQmojiPicker();
                        picker.dataset.initialized = 'true';
                    }}
                }} else {{
                    picker.classList.add('hidden');
                }}
            }}

            function initQmojiPicker() {{
                // 简化的 QQ 表情数据
                const qmojiData = {{
                    "经典": [
                        {{"name": "微笑", "code": "/微笑"}},
                        {{"name": "撇嘴", "code": "/撇嘴"}},
                        {{"name": "色", "code": "/色"}},
                        {{"name": "发呆", "code": "/发呆"}},
                        {{"name": "得意", "code": "/得意"}},
                        {{"name": "流泪", "code": "/流泪"}},
                        {{"name": "害羞", "code": "/害羞"}},
                        {{"name": "闭嘴", "code": "/闭嘴"}}
                    ]
                }};

                const content = document.querySelector('.qmoji-picker-content');
                content.innerHTML = '';

                Object.keys(qmojiData).forEach(category => {{
                    const categoryDiv = document.createElement('div');
                    categoryDiv.className = 'mb-4';
                    categoryDiv.innerHTML = `<h4 class="font-bold mb-2">${{category}}</h4>`;

                    const grid = document.createElement('div');
                    grid.className = 'grid grid-cols-8 gap-2';

                    qmojiData[category].forEach(emoji => {{
                        const button = document.createElement('button');
                        button.className = 'btn btn-sm btn-ghost p-2 hover:bg-base-200';
                        button.textContent = emoji.name;
                        button.title = emoji.code;
                        button.onclick = () => insertQmoji(emoji.code);
                        grid.appendChild(button);
                    }});

                    categoryDiv.appendChild(grid);
                    content.appendChild(categoryDiv);
                }});
            }}

            function insertQmoji(code) {{
                if (vditor) {{
                    vditor.insertValue(code);
                    toggleQmojiPicker(false);
                }}
            }}

            // 保存快捷键
            document.addEventListener('keydown', function(e) {{
                if (e.ctrlKey && e.key === 's') {{
                    e.preventDefault();
                    const content = vditor ? vditor.getValue() : '';
                    sendWebSocketMessage('save_request', content);
                }}
            }});

            // 初始化应用
            document.addEventListener("DOMContentLoaded", initEditor);
        </script>
    </body>
</html>
        """
