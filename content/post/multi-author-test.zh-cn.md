+++
title = 'Multi-Author Test'
date = 2025-01-27T20:00:00+08:00
draft = false
author = "spixed"
keywords = ["test"]
categories = ["test"]
tags = ["test"]
noclick = true
status = true
statusCate = "Big update! "
weight = 0
+++

这是一篇用于测试多作者功能的精选文章。

<!--more-->

## 功能特性

- ✅ 多作者支持
- ✅ 精选文章标识
- ✅ 正(n+1)边形作者选择器
- ✅ 响应式设计
- ✅ 动画效果

## 测试内容

这篇文章用于验证：

1. **作者信息显示**：文章底部应该显示作者头像和昵称
2. **精选标识**：文章卡片应该有金色边框和Pin图标
3. **过滤功能**：选择不同作者时文章应该正确过滤
4. **响应式布局**：在不同设备上都能正常显示

## 预期效果

- 文章卡片有金色渐变边框
- 左上角有Pin图标
- 底部显示作者信息
- 支持按作者过滤显示
